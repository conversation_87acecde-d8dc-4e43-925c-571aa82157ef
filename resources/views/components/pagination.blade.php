@props(['paginator'])

@if ($paginator->hasPages())
    <nav aria-label="Page navigation">
        <ul class="pagination mb-0 flex-nowrap pagination-seamless">
            {{-- Previous Page Link --}}
            @if ($paginator->onFirstPage())
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="bx bx-chevron-left"></i>
                    </span>
                </li>
            @else
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->previousPageUrl() }}" rel="prev">
                        <i class="bx bx-chevron-left"></i>
                    </a>
                </li>
            @endif

            {{-- Pagination Elements --}}
            @php
                $start = max(1, $paginator->currentPage() - 2);
                $end = min($paginator->lastPage(), $paginator->currentPage() + 2);
            @endphp

            {{-- First page --}}
            @if ($start > 1)
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->url(1) }}">1</a>
                </li>
                @if ($start > 2)
                    <li class="page-item disabled" aria-disabled="true">
                        <span class="page-link">
                            <i class="bx bx-dots-horizontal-rounded"></i>
                        </span>
                    </li>
                @endif
            @endif

            {{-- Page range --}}
            @for ($page = $start; $page <= $end; $page++)
                @if ($page == $paginator->currentPage())
                    <li class="page-item active" aria-current="page">
                        <span class="page-link">{{ $page }}</span>
                    </li>
                @else
                    <li class="page-item">
                        <a class="page-link" href="{{ $paginator->url($page) }}">{{ $page }}</a>
                    </li>
                @endif
            @endfor

            {{-- Last page --}}
            @if ($end < $paginator->lastPage())
                @if ($end < $paginator->lastPage() - 1)
                    <li class="page-item disabled" aria-disabled="true">
                        <span class="page-link">
                            <i class="bx bx-dots-horizontal-rounded"></i>
                        </span>
                    </li>
                @endif
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->url($paginator->lastPage()) }}">{{ $paginator->lastPage() }}</a>
                </li>
            @endif

            {{-- Next Page Link --}}
            @if ($paginator->hasMorePages())
                <li class="page-item">
                    <a class="page-link" href="{{ $paginator->nextPageUrl() }}" rel="next">
                        <i class="bx bx-chevron-right"></i>
                    </a>
                </li>
            @else
                <li class="page-item disabled" aria-disabled="true">
                    <span class="page-link">
                        <i class="bx bx-chevron-right"></i>
                    </span>
                </li>
            @endif
        </ul>
    </nav>

    {{-- Seamless Pagination Styling --}}
    <style>
        .pagination-seamless .page-item {
            margin: 0 !important;
        }

        .pagination-seamless .page-item .page-link {
            border-radius: 0 !important;
            border-right: 0;
            min-width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.375rem 0.75rem;
            font-size: 0.8125rem;
            color: var(--default-text-color, #6c757d);
            background-color: var(--custom-white, #fff);
            border: 1px solid var(--default-border, #dee2e6);
            transition: all 0.15s ease-in-out;
        }

        .pagination-seamless .page-item:first-child .page-link {
            border-top-left-radius: 0.375rem !important;
            border-bottom-left-radius: 0.375rem !important;
        }

        .pagination-seamless .page-item:last-child .page-link {
            border-top-right-radius: 0.375rem !important;
            border-bottom-right-radius: 0.375rem !important;
            border-right: 1px solid var(--default-border, #dee2e6);
        }

        .pagination-seamless .page-item.active .page-link {
            background-color: rgb(var(--secondary-rgb));
            border-color: rgb(var(--secondary-rgb));
            color: white;
            border-right: 1px solid rgb(var(--secondary-rgb));
        }

        .pagination-seamless .page-item:hover .page-link:not(.active) {
            background-color: var(--bs-light, #f8f9fa);
            color: rgb(var(--secondary-rgb));
            border-color: var(--default-border, #dee2e6);
        }

        .pagination-seamless .page-item.disabled .page-link {
            opacity: 0.6;
            pointer-events: none;
            color: var(--default-text-color, #6c757d);
            background-color: var(--custom-white, #fff);
            border-color: var(--default-border, #dee2e6);
        }

        /* Dark Mode Styles */
        [data-theme-mode="dark"] .pagination-seamless .page-item .page-link {
            color: var(--default-text-color);
            background-color: var(--custom-white);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item:last-child .page-link {
            border-right: 1px solid var(--default-border);
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item.active .page-link {
            background-color: rgb(var(--secondary-rgb));
            border-color: rgb(var(--secondary-rgb));
            color: white;
            border-right: 1px solid rgb(var(--secondary-rgb));
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item:hover .page-link:not(.active) {
            background-color: rgb(var(--light-rgb));
            color: rgb(var(--secondary-rgb));
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .pagination-seamless .page-item.disabled .page-link {
            opacity: 0.6;
            pointer-events: none;
            color: var(--default-text-color);
            background-color: var(--custom-white);
            border-color: var(--default-border);
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .pagination-seamless .page-item .page-link {
                min-width: 2.25rem;
                height: 2.25rem;
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
        }

        @media (max-width: 576px) {
            .pagination-seamless .page-item .page-link {
                min-width: 1.75rem;
                height: 1.75rem;
                font-size: 0.65rem;
                padding: 0.2rem 0.3rem;
            }

            /* Ensure pagination container doesn't overflow */
            .pagination-seamless {
                overflow-x: auto;
                white-space: nowrap;
            }
        }

        /* Extra small screens - even more compact */
        @media (max-width: 400px) {
            .pagination-seamless .page-item .page-link {
                min-width: 1.5rem;
                height: 1.5rem;
                font-size: 0.6rem;
                padding: 0.15rem 0.25rem;
            }
        }
    </style>
@endif
